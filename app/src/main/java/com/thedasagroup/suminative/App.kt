package com.thedasagroup.suminative

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.icu.util.Calendar
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.airbnb.mvrx.mocking.MockableMavericks
import com.github.markowanga.timberloggingtofile.LogToFileTimberTree
import com.github.markowanga.timberloggingtofile.logname.ConstantDateTimeLogFileNameProvider
import com.google.android.gms.tasks.Tasks
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.instacart.truetime.time.TrueTimeImpl
import com.pluto.Pluto
import com.pluto.plugins.network.PlutoNetworkPlugin
import com.sumup.merchant.reader.api.SumUpState
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import com.thedasagroup.suminative.work.LogUploadManager
import com.thedasagroup.suminative.work.OrderSyncManager
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject


@HiltAndroidApp
class App : Application(), Application.ActivityLifecycleCallbacks, Configuration.Provider {

    private val TAG: String? = App::class.java.simpleName

    @Inject
    lateinit var prefs: Prefs

    @Inject
    lateinit var hourUtils: HourUtils

    var isActivityVisible = false

    @Inject
    lateinit var trueTime: TrueTimeImpl

    @Inject lateinit var workerFactory : HiltWorkerFactory

    @Inject lateinit var orderSyncManager: OrderSyncManager

    companion object{
        const val GUAVA_URL_KEY = "guava_url"
        const val GUAVA_TEST_API_KEY = "apiKeyTest"
        const val USE_GUAVA_TEST = "useGuavaTest"
        const val WAITER_IDLE_TIME = "WAITER_IDLE_TIME"
    }

    override fun onCreate() {
        super.onCreate()

        MockableMavericks.initialize(applicationContext)

        try {
            Log.d(TAG, "Initializing SumUp SDK")
            SumUpState.init(this)
            Log.d(TAG, "SumUp SDK initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing SumUp SDK", e)
        }

        orderSyncManager.schedulePeriodicSync(intervalMinutes = 15L)

        trueTime.sync()
//        initLogger()
//        TerminalApplicationDelegate.onCreate(this)

        if(prefs.loginResponse?.user?.email != null
            && prefs.loginResponse?.user?.password != null
            && prefs.store != null){
//            scheduleJob()
        }

        if(BuildConfig.DEBUG){
            Pluto.Installer(this)
                .addPlugin(PlutoNetworkPlugin())
                .install()
            Pluto.showNotch(true)
        }

        registerActivityLifecycleCallbacks(this)


        val firebaseRemoteConfig = FirebaseRemoteConfig.getInstance()

        // set in-app defaults
        val remoteConfigDefaults: MutableMap<String?, Any?> = mutableMapOf()
        remoteConfigDefaults[GUAVA_URL_KEY] = "https://api-pos.guavapay.com/v1"
        remoteConfigDefaults[GUAVA_TEST_API_KEY] = "8SaAyahCRzCs8c3V1qa8wg==.aa54ebef5d778782781d5821c0f9d3ba"
        remoteConfigDefaults[USE_GUAVA_TEST] = false
        firebaseRemoteConfig.setDefaultsAsync(remoteConfigDefaults)
        firebaseRemoteConfig.fetch(60) // fetch every minutes
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Log.d(TAG, "remote config is fetched.")
                    GlobalScope.launch(Dispatchers.IO){
                        val task = firebaseRemoteConfig.fetchAndActivate()
                        Tasks.await(task)
                    }
                }
            }

    }


    private fun scheduleJob(){
        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }

    fun initLogger() {
        val now = trueTime.now()
        val calendar = Calendar.getInstance()
        calendar.time = now
        val hour = calendar.get(Calendar.HOUR)
        val minute = calendar.get(Calendar.MINUTE)
        val portion = hourUtils.getPortion(hour = hour, minute = minute)
        val hourlyDateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd_HH_${portion}")

        Timber.plant(
            LogToFileTimberTree(
                logFileNameProvider = ConstantDateTimeLogFileNameProvider(
                    dateTime = LocalDateTime.now(),
                    dateTimeFormatter = hourlyDateTimeFormatter
                ),
                logsDirectory = filesDir
            )
        )

        // Schedule periodic log uploads
        LogUploadManager.schedulePeriodicLogUploads(this)
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityStarted(activity: Activity) {

    }

    override fun onActivityResumed(activity: Activity) {
        isActivityVisible = true
    }

    override fun onActivityPaused(activity: Activity) {
        isActivityVisible = false
    }

    override fun onActivityStopped(activity: Activity) {

    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityDestroyed(activity: Activity) {

    }


    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(Log.INFO)
            .build()
}