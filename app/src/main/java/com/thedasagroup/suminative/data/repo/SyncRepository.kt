package com.thedasagroup.suminative.data.repo

import android.util.Log
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.SYNC_ORDER_TO_TABLE
import com.thedasagroup.suminative.data.api.TOGGLE_TABLE_OCCUPIED
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.model.response.table_sync.TableResponse
import io.ktor.client.call.body
import io.ktor.client.request.delete
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Path
import timber.log.Timber

class SyncRepository : BaseRepository() {

    /**
     * Sync order to table using POST request with API client
     * @param request The sync order request containing table and order data
     */
    suspend fun syncOrderToTable(
        request: SyncOrderRequest
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = SYNC_ORDER_TO_TABLE) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val responseBody = httpResponse.body<SyncOrderResponse>()
                return@safeApiCall Success(responseBody)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Get synced order for table using GET request with Retrofit
     * @param tableId The table ID to get the synced order for
     */
    suspend fun getSyncedOrderForTable(
        tableId: Int
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getTableSyncRetrofitService()
                val retrofitResponse = service.getSyncedOrderForTable(tableId = tableId)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val syncOrderResponse = retrofitResponse.body()
                        if (syncOrderResponse != null) {
                            Success(syncOrderResponse)
                        } else {
                            Fail(Throwable("Empty response body for table sync order"))
                        }
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Toggle table occupied status using POST request with API client
     * @param tableId The table ID to toggle occupied status
     * @param netPayable The net payable amount for the table
     */
    suspend fun toggleTableOccupied(
        tableId: Int,
        netPayable: Double
    ): StateFlow<Async<TableResponse>> {
        val flow = MutableStateFlow<Async<TableResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = TOGGLE_TABLE_OCCUPIED) {
                    contentType(ContentType.Application.Json)
                    parameter("tableId", tableId)
                    parameter("netPayable", netPayable)
                }
                val responseBody = httpResponse.body<TableResponse>()
                return@safeApiCall Success(responseBody)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Update order for table using PUT request with API client
     * @param tableId The table ID to update the order for
     * @param request The update order request containing order data
     */
    suspend fun updateOrderForTable(
        tableId: Int,
        request: UpdateOrderRequest
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.put(urlString = "$SYNC_ORDER_TO_TABLE/$tableId") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val responseBody = httpResponse.body<SyncOrderResponse>()
                return@safeApiCall Success(responseBody)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Delete order for table using DELETE request with Retrofit
     * @param tableId The table ID to delete the order for
     */
    suspend fun deleteOrderForTable(
        tableId: Int
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getTableSyncRetrofitService()
                val retrofitResponse = service.deleteOrderForTable(tableId = tableId)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val syncOrderResponse = retrofitResponse.body()
                        if (syncOrderResponse != null) {
                            Success(syncOrderResponse)
                        } else {
                            Fail(Throwable("Empty response body for table order deletion"))
                        }
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }


    val intercepter = HttpLoggingInterceptor().apply {
        this.level = HttpLoggingInterceptor.Level.BODY
    }

    /**
     * Creates and configures the Retrofit service for Table Sync API
     */
    private fun getTableSyncRetrofitService(): TableSyncRetrofitService {
        val networkJson = Json { ignoreUnknownKeys = true }
        val client: OkHttpClient = OkHttpClient.Builder()
            .addInterceptor(intercepter)
            .build()
        val retrofit = Retrofit.Builder()
            .baseUrl("$BASE_DOMAIN/")
            .client(client)
            .addConverterFactory(networkJson.asConverterFactory("application/json".toMediaType()))
            .build()
        return retrofit.create(TableSyncRetrofitService::class.java)
    }


}

/**
 * Retrofit service interface for Table Sync API endpoints
 */
interface TableSyncRetrofitService {

    /**
     * Get synced order for a specific table
     *
     * @param tableId The table ID to get the synced order for
     * @return Response containing the synced order data
     */
    @GET("BackendDASA-1.0.0/api/tables/sync/order/{tableId}")
    suspend fun getSyncedOrderForTable(
        @Path("tableId") tableId: Int
    ): Response<SyncOrderResponse>

    /**
     * Delete order for a specific table
     *
     * @param tableId The table ID to delete the order for
     * @return Response containing the deletion result
     */
    @DELETE("BackendDASA-1.0.0/api/tables/sync/order/{tableId}")
    suspend fun deleteOrderForTable(
        @Path("tableId") tableId: Int
    ): Response<SyncOrderResponse>
}
