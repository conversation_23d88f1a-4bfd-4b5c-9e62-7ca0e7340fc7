package com.thedasagroup.suminative.data.prefs

import android.content.Context
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.login.Store
import com.thedasagroup.suminative.data.model.response.login.StoreSettings
import com.thedasagroup.suminative.data.model.response.login.User
import com.thedasagroup.suminative.data.model.response.store_configurations.StoreConfigurationsResponse
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class Prefs(context: Context) {
    private val sharedPrefs = context.getSharedPreferences("prefs", Context.MODE_PRIVATE)
    val jsonCall = Json {
        ignoreUnknownKeys = true
    }
    var loginResponse : LoginResponse?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("loginResponse").apply()
            else {
                val json = jsonCall.encodeToString(value)
                sharedPrefs.edit().putString("loginResponse", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("loginResponse", "") ?: ""
            if(json.isNotEmpty()) {
                val model = jsonCall.decodeFromString<LoginResponse>(json)
                return model
            }
            return null
        }

    var store : Store?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("store").apply()
            else {
                val json = jsonCall.encodeToString(value)
                sharedPrefs.edit().putString("store", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("store", "") ?: ""
            if(json.isNotEmpty()) {
                val model = jsonCall.decodeFromString<Store>(json)
                return model
            }
            return null
        }

    var storeSettings : StoreSettings?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("storeSettings").apply()
            else {
                val json = jsonCall.encodeToString(value)
                sharedPrefs.edit().putString("storeSettings", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("storeSettings", "") ?: ""
            if(json.isNotEmpty()) {
                val model = jsonCall.decodeFromString<StoreSettings>(json)
                return model
            }
            return null
        }

    var storeConfigurations : StoreConfigurationsResponse?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("storeConfigurations").apply()
            else {
                val json = jsonCall.encodeToString(value)
                sharedPrefs.edit().putString("storeConfigurations", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("storeConfigurations", "") ?: ""
            if(json.isNotEmpty()) {
                val model = jsonCall.decodeFromString<StoreConfigurationsResponse>(json)
                return model
            }
            return null
        }
    var storeClosed : Boolean
        set(value) {
            sharedPrefs.edit().putBoolean("storeClosed", value).apply()
        }
        get() {
            return sharedPrefs.getBoolean("storeClosed", false)
        }


    var password : String
        set(value) {
            sharedPrefs.edit().putString("password", value).apply()
        }
        get() {
            return sharedPrefs.getString("password", "") ?: ""
        }

    var selectedWaiter : User?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("waiter").apply()
            else {
                val json = jsonCall.encodeToString(value)
                sharedPrefs.edit().putString("waiter", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("waiter", "") ?: ""
            if(json.isNotEmpty()) {
                val model = jsonCall.decodeFromString<User>(json)
                return model
            }
            return null
        }
}

fun LoginResponse.validate(): Boolean{
    return this.user?.id != null
}
