package com.thedasagroup.suminative.ui.login

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.EncryptedNdk
import com.thedasagroup.suminative.data.model.request.login2.LoginRequest2
import com.thedasagroup.suminative.data.model.request.store_configurations.StoreConfigurationsRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class LoginUseCase(private val loginRepository: LoginRepository, private val prefs: Prefs) {
    suspend operator fun invoke(email: String, password: String, shouldEncrypt: Boolean): StateFlow<Async<LoginResponse>> {

        val publicKey = decodePublicKey(BuildConfig.APITOKEN)

        val encryptedPassword = if(shouldEncrypt) encrypt(password, publicKey) else password

        val loginResponse = loginRepository.login(
            loginRequest = LoginRequest2(
                email = email, password = encryptedPassword.replace("\n", "")
            )
        )

        val response = when(loginResponse.value){
            is Success<*> -> {
                val storeConfigurationsRequest = StoreConfigurationsRequest(
                    storeId = loginResponse.value()?.stores?.firstOrNull()?.id ?: 0
                )
                val storeConfigurationsResponse = loginRepository.getStoreConfigurations(storeConfigurationsRequest)
                when(storeConfigurationsResponse.value){
                    is Success<*> -> {
                        prefs.storeConfigurations = storeConfigurationsResponse.value()
                        prefs.loginResponse = loginResponse.value()
                        prefs.password = password
                        val user = loginResponse.value()?.user
                        if(user  != null){
                            prefs.selectedWaiter = loginResponse.value()?.user
                        }
                        loginResponse
                    }
                    is Fail<*> -> {
                        MutableStateFlow(Fail(Throwable((storeConfigurationsResponse.value as Fail<*>).error.message)))
                    }
                    else -> {
                        loginResponse
                    }
                }
            }
            else -> {
                loginResponse
            }
        }
        return response
    }
}
// test changes git abc