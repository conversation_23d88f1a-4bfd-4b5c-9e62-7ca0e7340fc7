package com.thedasagroup.suminative.ui.orders

import android.media.AudioManager
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Order
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.domain.GetPOSSettingsUseCase
import com.thedasagroup.suminative.ui.StockManagement
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.getMinutesBetweenTwoDates
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone

class OrderScreenViewModel @AssistedInject constructor(
    @Assisted state: OrderState,
    val getOrdersUseCase: GetOrdersUseCase,
    val getPendingOrdersPagedUseCase: GetPendingOrdersPagedUseCase,
    val getScheduleOrdersUseCase: GetScheduleOrdersUseCase,
    val getScheduleOrdersPagedUseCase: GetScheduleOrdersPagedUseCase,
    val changeStatusUseCase: ChangeStatusUseCase,
    val changeStatus: ChangeStatusAndOrdersUseCase,
    val acceptOrderWithDelayUseCase: AcceptOrderWithDelayUseCase,
    val trueTimeImpl: TrueTimeImpl,
    val ordersRepository: OrdersRepository,
    val getStoreSettings: GetStoreSettingsUseCase,
    val closeOpenStoreUseCase: CloseOpenStoreUseCase,
    val prefs: Prefs,
    val soundPoolPlayer: SoundPoolPlayer,
    val audioManager: AudioManager,
    val getPosSettings: GetPOSSettingsUseCase,
) : MavericksViewModel<OrderState>(state) {

    init {
        viewModelScope.launch(Dispatchers.IO) {
            getIsClosed()
        }
    }

    suspend fun getOrders(isShowAllOrders: Boolean): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        setState {
            copy(ordersResponse = Loading())
        }
        getPendingOrdersPagedUseCase(false).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        ordersResponse = it(),
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        ordersResponse = Uninitialized,
                    )
                }
            }
        }
        return flow
    }

    suspend fun getScheduleOrders(): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        setState {
            copy(scheduleOrdersResponse = Loading())
        }
        getScheduleOrdersPagedUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        scheduleOrdersResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        scheduleOrdersResponse = Uninitialized
                    )
                }
            }
        }
        return flow
    }

    suspend fun getOrdersSilent(isShowAllOrders: Boolean): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        getPendingOrdersPagedUseCase(isShowAllOrders).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        ordersResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        ordersResponse = Uninitialized,
                    )
                }
            }
        }
        return flow
    }

    suspend fun getScheduleOrdersSilent(): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        getScheduleOrdersPagedUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        scheduleOrdersResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        scheduleOrdersResponse = Uninitialized,
                    )
                }
            }
        }
        return flow
    }

    suspend fun changeStatus(orderId: Int, status: Int): StateFlow<Async<ChangeStatusResponse>> {
        val flow = MutableStateFlow<Async<ChangeStatusResponse>>(Loading())
        setState {
            copy(changeStatusResponse = Loading())
        }
        changeStatusUseCase(orderId = orderId, status = status).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        changeStatusResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(changeStatusResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun changeStatusOfOrder(
        orderId: Int, status: Int, isShowAllOrders: Boolean
    ): StateFlow<Async<OrderResponse?>> {
        val flow = MutableStateFlow<Async<OrderResponse?>>(Loading())
        setState {
            copy(acceptDeliveryOrderResponse = Loading())
        }
        changeStatus(
            orderId = orderId, status = status, isShowAllOrder = isShowAllOrders
        ).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        acceptDeliveryOrderResponse = it(),
                        ordersResponse = Success(it()() ?: OrderResponse()),
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        acceptDeliveryOrderResponse = Uninitialized,
                    )
                }
            }
        }
        return flow
    }

    suspend fun acceptOrderWithDelay(
        orderId: Int, isShowAllOrders: Boolean, delayInMinutes: Int
    ): StateFlow<Async<OrderResponse?>> {
        val flow = MutableStateFlow<Async<OrderResponse?>>(Loading())
        setState {
            copy(acceptDeliveryOrderResponse = Loading())
        }
        acceptOrderWithDelayUseCase(
            orderId = orderId, isShowAllOrder = isShowAllOrders, delayInMinutes = delayInMinutes
        ).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        acceptDeliveryOrderResponse = it(),
                        ordersResponse = Success(it()() ?: OrderResponse())
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        acceptDeliveryOrderResponse = Uninitialized
                    )
                }
            }
        }
        return flow
    }

    fun updateExpanded(expanded: Boolean) {
        setState {
            copy(isExpanded = expanded)
        }
    }

    fun updateShowAllOrders(value: Boolean) {
        setState {
            copy(isShowAllOrders = value)
        }
    }

    fun updateOrders(ordersResponse: OrderResponse) {
        setState {
            copy(ordersResponse = Success(ordersResponse))
        }
    }

    fun updateShowPrintingPreview(order: OrderItem?, shouldPrintInstant: Boolean = false) {
        setState {
            copy(isShowPrintingPreview = order, shouldPrintInstant = shouldPrintInstant)
        }
    }

    fun updateClickedOrderId(orderId: Int) {
        setState {
            copy(clickedOrderId = orderId)
        }
    }

    fun updateSelectedChangeStatusId(statusId: Int) {
        setState {
            copy(selectedChangeStatusId = statusId)
        }
    }

    fun updateShowChangeStatusDialog(order: OrderItem?) {
        setState {
            copy(isShowChangeStatusDialog = order)
        }
    }

    fun updateChangeStatusOrder(order: OrderItem?) {
        setState {
            copy(changeStatusOrder = order)
        }
    }

    fun updateAcceptOrderDelay(delay: Int) {
        setState {
            copy(acceptOrderDelay = delay)
        }
    }

    fun updateShowAcceptOrderDelayDialog(order: OrderItem?) {
        setState {
            copy(showAcceptOrderDelayDialog = order)
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<OrderScreenViewModel, OrderState> {
        override fun create(state: OrderState): OrderScreenViewModel
    }

    fun updateStoreCloseSettings(storeCloseSettings: String) {
        setState {
            copy(storeCloseSettings = storeCloseSettings)
        }
    }

    suspend fun updateIsStoreClosedManual(isStoreClosedManual: Boolean): StateFlow<Async<CloseOpenStoreResponse>> {
        val flow = MutableStateFlow<Async<CloseOpenStoreResponse>>(Loading())
        setState {
            copy(closedStoreManualResponse = Loading())
        }
        closeOpenStoreUseCase(
            closed = isStoreClosedManual
        ).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        closedStoreManualResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        closedStoreManualResponse = Uninitialized
                    )
                }
            }
        }
        return flow
    }

    suspend fun getIsClosed(): StateFlow<Async<CloseOpenStoreResponse>> {
        val flow = MutableStateFlow<Async<CloseOpenStoreResponse>>(Loading())
        setState {
            copy(closedStoreManualResponse = Loading())
        }
        ordersRepository.isClosed(storeId = (prefs.store?.id ?: 105).toString()).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        closedStoreManualResponse = it()
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        closedStoreManualResponse = Uninitialized
                    )
                }
            }
        }
        return flow
    }

    suspend fun getStoreSettingsFun(): StateFlow<Async<StoreSettingsResponse>> {
        val flow = MutableStateFlow<Async<StoreSettingsResponse>>(Loading())
        setState {
            copy(storeSettingsResponse = Loading())
        }
        getPosSettings().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(
                        storeSettingsResponse = it(),
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(
                        storeSettingsResponse = Uninitialized,
                    )
                }
            }
        }
        return flow
    }

    fun updateStoreTimings(openingTime: String, closeTime: String) {
        setState {
            copy(openingTime = openingTime, closeTime = closeTime)
        }
    }

    fun updateShowDialog(
        dialogType: String,
        dialogMessage: String,
        dialogTitle: String,
        showDialog: String,
        isScheduleOrder: Boolean = false
    ) {
        setState {
            copy(
                dialogType = dialogType,
                dialogMessage = dialogMessage,
                dialogTitle = dialogTitle,
                isScheduleOrder = isScheduleOrder
            )
        }
    }

    fun getCurrentUTC(): String {
        val time: Date = trueTimeImpl.now()
        val outputFmt = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        outputFmt.timeZone = TimeZone.getTimeZone("UTC")
        return outputFmt.format(time)
    }

    fun updateIsConnected(isConnected2: Boolean) {
        setState {
            copy(isConnected2 = isConnected2)
        }
    }

    fun updateDialogType(dialogType: String) {
        setState {
            copy(dialogType = dialogType)
        }
    }

    fun updateCurrentRoute(currentRoute: String) {
        setState {
            copy(currentRouteId = currentRoute)
        }
    }

    fun goToStoreItemsAndSelectTable() {
        setState {
            copy(currentRouteId = "0")
        }
    }
    fun isNavSetupDone(isNavSetupDone: Boolean){
        setState {
            copy(isNavSetupDone = isNavSetupDone)
        }
    }

    fun updateIsScheduleOrder(isScheduleOrder: Boolean) {
        setState {
            copy(isScheduleOrder = isScheduleOrder)
        }
    }

    companion object :
        MavericksViewModelFactory<OrderScreenViewModel, OrderState> by hiltMavericksViewModelFactory()
}

data class OrderState(
    val ordersResponse: Async<OrderResponse> = Uninitialized,
    val scheduleOrdersResponse: Async<OrderResponse> = Uninitialized,
    val changeStatusResponse: Async<ChangeStatusResponse> = Uninitialized,
    val isExpanded: Boolean = false,
    val isShowAllOrders: Boolean = false,
    val isShowPrintingPreview: OrderItem? = null,
    val clickedOrderId: Int = -1,
    val selectedChangeStatusId: Int = -1,
    val isShowChangeStatusDialog: OrderItem? = null,
    val showAcceptOrderDelayDialog: OrderItem? = null,
    val changeStatusOrder: OrderItem? = null,
    val acceptDeliveryOrderResponse: Async<OrderResponse?> = Uninitialized,
    val acceptOrderDelay: Int = 0,
    val mapCount: Map<Int?, Int> = mutableMapOf(),
    val shouldPrintInstant: Boolean = false,
    val storeCloseSettings: String = "store_open",
    val closedStoreManualResponse: Async<CloseOpenStoreResponse> = Uninitialized,
    val openingTime: String = "",
    val closeTime: String = "",
    val dialogType: String = "",
    val dialogMessage: String = "",
    val dialogTitle: String = "",
    val isConnected2: Boolean = false,
    val currentRouteId: String = "",
    val isScheduleOrder: Boolean = false,
    val isNavSetupDone: Boolean = false,
    val storeSettingsResponse: Async<StoreSettingsResponse> = Uninitialized,
) : MavericksState