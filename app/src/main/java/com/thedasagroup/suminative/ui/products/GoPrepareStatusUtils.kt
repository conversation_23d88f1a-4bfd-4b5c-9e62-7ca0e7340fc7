package com.thedasagroup.suminative.ui.products

/**
 * Check if a course should show the Go button
 * @param courseId The course to check
 * @return true if this course should show the Go button
 */
fun shouldShowGoButton(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val currentTableId = state.getCurrentTableId()
    val queue = getCourseStatusQueue(currentTableId, state)

    // Check if course is not in preparing or completed queues
    val notInOtherQueues = queue.preparingQueue.firstOrNull {
        it == courseId
    }?.isNotEmpty() != true && queue.completedCourses.firstOrNull {
        it == courseId
    }?.isNotEmpty() != true

    // Find index of course in go queue
    val goQueueIndex = queue.goQueue.indexOfFirst { it == courseId }
    
    // Show Go button only for first item in go queue
    return notInOtherQueues && goQueueIndex == 0 && queue.goQueue.isNotEmpty()
}


fun shouldShowPreparing(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val currentTableId = state.getCurrentTableId()
    val queue = getCourseStatusQueue(currentTableId, state)

    if(courseId.isNotEmpty()) {
        return queue.goQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.preparingQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() == true && queue.completedCourses.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true
    }
    else {
        return queue.goQueue.firstOrNull {
            it == courseId
        } == null && queue.preparingQueue.firstOrNull {
            it == courseId
        } != null && queue.completedCourses.firstOrNull {
            it == courseId
        } == null
    }
}


fun shouldShowComplete(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val currentTableId = state.getCurrentTableId()
    val queue = getCourseStatusQueue(currentTableId, state)

    if(courseId.isNotEmpty()) {
        return queue.goQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.preparingQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.completedCourses.firstOrNull {
            it == courseId
        }?.isNotEmpty() == true
    }
    else {
        return queue.goQueue.firstOrNull {
            it == courseId
        } == null && queue.preparingQueue.firstOrNull {
            it == courseId
        } == null && queue.completedCourses.firstOrNull {
            it == courseId
        } != null
    }
}

/**
 * Get the current course status queue for the given context
 */
fun getCourseStatusQueue(currentTableId: Int?, state: ProductsScreenState): CourseStatusQueue {
    return if (currentTableId != null) {
        state.tableCourseStatusQueues[currentTableId] ?: CourseStatusQueue()
    } else {
        state.courseStatusQueue
    }
}