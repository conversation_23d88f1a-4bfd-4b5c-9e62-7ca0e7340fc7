package com.thedasagroup.suminative.ui.rewards

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.rewards.RewardItem
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel

/**
 * Helper class to add reward items to cart with zero price
 */
object RewardsCartHelper {
    
    /**
     * Adds a reward item to the cart with zero price
     * 
     * @param rewardItem The reward item to add
     * @param productsViewModel The products screen view model
     * @param state Current products screen state
     * @param quantity Quantity to add (default 1)
     */
    fun addRewardItemToCart(
        rewardItem: RewardItem,
        productsViewModel: ProductsScreenViewModel,
        state: ProductsScreenState,
        quantity: Int = 1
    ) {
        // Convert reward item to store item with zero price
        val storeItem = rewardItem.toZeroPriceStoreItem()
        
        // Create empty option details since rewards typically don't have options
        val optionDetails = OptionDetails()
        
        // Add to cart using the existing ProductsScreenViewModel method
        productsViewModel.addItemToCart(
            order = state.order,
            storeItem = storeItem,
            optionDetails = optionDetails,
            quantity = quantity,
            tableOrders = state.tableOrders,
            selectedTableIndex = state.selectedTableIndex,
            selectedTables = state.selectedTables,
            state = state
        )
    }
    
    /**
     * Manually adds a reward item to cart by creating a Cart object directly
     * This is useful when you want more control over the cart addition process
     */
    fun addRewardItemToCartManually(
        rewardItem: RewardItem,
        currentOrder: Order,
        quantity: Int = 1
    ): Order {
        val storeItem = rewardItem.toZeroPriceStoreItem()
        
        // Create a new cart item with zero price
        val cartItem = Cart(
            storeItem = storeItem,
            quantity = quantity,
            price = 0.0, // Zero price for rewards
            extraPrice = 0.0,
            tax = 0.0, // Zero tax for rewards
            discount = 0.0,
            netPayable = 0.0, // Zero net payable for rewards
            optionPrice = 0.0,
            isB1G1 = false,
            notes = "Reward Item - FREE"
        )
        
        // Add to existing cart
        val updatedCarts = currentOrder.carts?.toMutableList() ?: mutableListOf()
        updatedCarts.add(cartItem)
        
        // Return updated order
        return currentOrder.copy(
            carts = updatedCarts,
            // Update totals (rewards don't affect totals since they're free)
            totalPrice = currentOrder.totalPrice,
            netPayable = currentOrder.netPayable,
            tax = currentOrder.tax
        )
    }
    
    /**
     * Converts a RewardItem to a StoreItem with zero price
     */
    private fun RewardItem.toZeroPriceStoreItem(): StoreItem {
        val rewardStoreItem = this.storeItem
        return StoreItem(
            id = rewardStoreItem?.id,
            name = rewardStoreItem?.name,
            description = rewardStoreItem?.description,
            pic = rewardStoreItem?.pic,
            price = 0.0, // Zero price for rewards
            billAmount = 0.0, // Zero bill amount for rewards
            tax = 0.0, // Zero tax for rewards
            discountedAmount = 0.0,
            businessId = rewardStoreItem?.businessId,
            storeId = rewardStoreItem?.storeId,
            brandId = rewardStoreItem?.brandId,
            categoryId = rewardStoreItem?.categoryId,
            unitId = rewardStoreItem?.unitId ?: -1,
            discountType = rewardStoreItem?.discountType ?: 0,
            ingredients = rewardStoreItem?.ingredients,
            preparationTime = rewardStoreItem?.preparationTime,
            additionalInfo = rewardStoreItem?.additionalInfo,
            dailyCapacity = rewardStoreItem?.dailyCapacity ?: 0,
            createdBy = rewardStoreItem?.createdBy,
            createdOn = rewardStoreItem?.createdOn,
            modifiedBy = rewardStoreItem?.modifiedBy,
            modifiedOn = rewardStoreItem?.modifiedOn,
            servingSize = rewardStoreItem?.servingSize,
            quantity = 1,
            vat = rewardStoreItem?.vat ?: false,
            discounttypename = "Reward",
            unitName = "Unit",
            optionSets = emptyList(),
            extras = emptyList()
        )
    }
    
    /**
     * Converts StoreItem to StockItem for compatibility with ProductsScreenViewModel
     */
    private fun StoreItem.toStockItem(): com.thedasagroup.suminative.data.model.response.stock.StockItem {
        return com.thedasagroup.suminative.data.model.response.stock.StockItem(
            id = this.id,
            name = this.name,
            description = this.description,
            pic = this.pic,
            price = this.price,
            billAmount = this.billAmount,
            tax = this.tax,
            businessId = this.businessId,
            storeId = this.storeId,
            brandId = this.brandId,
            categoryId = this.categoryId,
            unitId = this.unitId,
            discountType = this.discountType,
            discountedAmount = this.discountedAmount?.toString(),
            ingredients = this.ingredients,
            preparationTime = this.preparationTime,
            additionalInfo = this.additionalInfo,
            dailyCapacity = this.dailyCapacity,
            createdBy = this.createdBy,
            createdOn = this.createdOn,
            modifiedBy = this.modifiedBy,
            modifiedOn = this.modifiedOn,
            servingSize = this.servingSize,
            stock = this.quantity,
            vat = this.vat,
            category = "Rewards" // Set category to indicate it's a reward
        )
    }
}
