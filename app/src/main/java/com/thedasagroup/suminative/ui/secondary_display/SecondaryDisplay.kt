package com.thedasagroup.suminative.ui.secondary_display

import android.content.Context
import android.hardware.display.DisplayManager
import android.util.Log
import android.view.Display
import com.thedasagroup.suminative.ui.MainActivity


fun MainActivity.getPresentationDisplays(): Display? {
    val mDisplayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
    displays = mDisplayManager.displays
    for (i in 0..<displays.size) {
        Log.e("MainActivity", "Screen" + displays[i])
        if ((displays[i].getFlags() and Display.FLAG_SECURE) !== 0 && (displays[i].getFlags() and Display.FLAG_SUPPORTS_PROTECTED_BUFFERS) !== 0 && (displays[i].getFlags() and Display.FLAG_PRESENTATION) !== 0) {
            Log.e("MainActivity", "First real second screen" + displays[i])
            return displays[i]
        }
    }

    return null
}